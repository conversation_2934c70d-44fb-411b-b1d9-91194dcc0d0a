// apps/mobile/src/providers/QueryProvider.tsx
import React, { useState } from 'react';
import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createAsyncStoragePersister } from '@tanstack/query-async-storage-persister';
import AsyncStorage from '@react-native-async-storage/async-storage';

const asyncStoragePersister = createAsyncStoragePersister({
  storage: AsyncStorage,
  key: 'DOHODER_QUERY_CACHE',
});

export const QueryProvider = ({ children }: { children: React.ReactNode }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 0, // dáta sú stale hneď = vždy refetch
            gcTime: 10 * 60 * 1000, // 10 min
            refetchOnMount: true, // keď sa mountne komponent
            refetchOnReconnect: true, // keď sa obnoví sieť
            refetchOnWindowFocus: true, // keď user appku otvorí
          },
        },
      })
  );

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister: asyncStoragePersister,
        maxAge: 1000 * 60 * 60 * 24 * 7, // 7 dní
      }}
    >
      {children}
    </PersistQueryClientProvider>
  );
};

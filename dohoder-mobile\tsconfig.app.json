{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "noUnusedLocals": false, "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo", "types": []}, "files": ["../node_modules/@nx/expo/typings/svg.d.ts"], "exclude": ["out-tsc", "dist", "**/*.test.ts", "**/*.spec.ts", "**/*.test.tsx", "**/*.spec.tsx", "**/*.test.js", "**/*.spec.js", "**/*.test.jsx", "**/*.spec.jsx", "src/test-setup.ts", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "jest.resolver.js", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"], "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "references": [{"path": "../libs/data-access/tsconfig.lib.json"}, {"path": "../libs/domain/tsconfig.lib.json"}, {"path": "../libs/util/tsconfig.lib.json"}]}
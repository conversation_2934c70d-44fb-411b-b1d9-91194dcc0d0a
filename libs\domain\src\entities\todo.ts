export interface Todo {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  userId: string;
  categoryId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTodoRequest {
  title: string;
  description?: string;
  userId: string;
  categoryId?: string;
}

export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  completed?: boolean;
  categoryId?: string;
}

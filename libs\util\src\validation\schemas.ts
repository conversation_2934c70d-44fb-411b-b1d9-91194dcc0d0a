/**
 * Common validation utilities
 */

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPassword(password: string): boolean {
  // At least 8 characters, one uppercase, one lowercase, one number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function validateUser(data: { email?: string; name?: string; password?: string }): ValidationResult {
  const errors: string[] = [];

  if (data.email && !isValidEmail(data.email)) {
    errors.push('Neplatný email formát');
  }

  if (data.password && !isValidPassword(data.password)) {
    errors.push('Heslo musí mať aspoň 8 znakov, jedno veľké písmeno, jedno malé písmeno a jednu číslicu');
  }

  if (data.name && data.name.trim().length < 2) {
    errors.push('Meno musí mať aspoň 2 znaky');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

import React from 'react';
import { View, Text, FlatList, StyleSheet } from 'react-native';
import { formatDate, formatRelativeTime } from '@/utils/date';
import { Todo } from '@/domain/entities/todo';

interface TodoListProps {
  todos: Todo[];
}

export function TodoList({ todos }: TodoListProps) {
  const renderTodo = ({ item }: { item: Todo }) => (
    <View style={styles.todoItem}>
      <Text style={styles.title}>{item.title}</Text>
      {item.description && (
        <Text style={styles.description}>{item.description}</Text>
      )}
      <Text style={styles.date}>
        Vytvorené: {formatDate(new Date(item.createdAt))} 
        ({formatRelativeTime(new Date(item.createdAt))})
      </Text>
      <View style={styles.statusContainer}>
        <Text style={[styles.status, item.completed && styles.completed]}>
          {item.completed ? 'Dokončené' : 'Nedokončené'}
        </Text>
      </View>
    </View>
  );

  return (
    <FlatList
      data={todos}
      renderItem={renderTodo}
      keyExtractor={(item) => item.id}
      style={styles.container}
      showsVerticalScrollIndicator={false}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  todoItem: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    marginBottom: 12,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  date: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  statusContainer: {
    alignSelf: 'flex-start',
  },
  status: {
    fontSize: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#FFA500',
    color: 'white',
    overflow: 'hidden',
  },
  completed: {
    backgroundColor: '#4CAF50',
  },
});

import './global.css';
import { StyledComponentsRegistry } from './registry';
import { QueryProvider } from './providers/query-provider';

export const metadata = {
  title: 'DOHODER Admin',
  description: 'Administračný panel pre DOHODER aplikáciu',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="sk">
      <body>
        <StyledComponentsRegistry>
          <QueryProvider>{children}</QueryProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}

# Import Guide - Optimalizované importy pre DOHODER projekt

## 🎯 Nový import systém

Projekt teraz používa čistý a intuitívny import systém s `@/` prefixom namiesto dlhých `@dohoder/` názvov.

## 📁 Štruktúra knižníc

### Domain (`@/domain`)
Obsahuje business logiku, entity a služby.

```typescript
// Importy entít
import { User, Todo, CreateUserRequest } from '@/domain/entities';
import { User } from '@/domain/entities/user';
import { Todo } from '@/domain/entities/todo';

// Importy služieb
import { UserService } from '@/domain/services';
import { UserService } from '@/domain/services/user-service';

// Barrel import (všetko naraz)
import { User, Todo, UserService } from '@/domain';
```

### Data Access (`@/data-access`)
Obsahuje databázové pripojenia, API klientov a data layer.

```typescript
// Supabase klient
import { createClient } from '@/data-access/supabase/next-server';

// Budúce API klienty
import { apiClient } from '@/data-access/api/client';
import { userRepository } from '@/data-access/repositories/user-repository';

// Barrel import
import { createClient } from '@/data-access';
```

### UI Components (`@/ui`)
Obsahuje znovupoužiteľné UI komponenty.

```typescript
// Špecifické komponenty
import { Button } from '@/ui/components/button';
import { Modal } from '@/ui/components/modal';
import { Form } from '@/ui/components/form';

// Barrel import
import { Button, Modal, Form } from '@/ui/components';
import { Button } from '@/ui';
```

### Utils (`@/utils`)
Obsahuje utility funkcie a helpery.

```typescript
// Špecifické utility
import { formatDate, formatRelativeTime } from '@/utils/date';
import { isValidEmail, validateUser } from '@/utils/validation';

// Barrel import
import { formatDate, isValidEmail } from '@/utils';
```

## 🔄 Migrácia zo starého systému

### Pred (staré importy):
```typescript
import { createClient } from '@dohoder/data-access';
import { SomeUtil } from '@dohoder/util/some-util';
```

### Po (nové importy):
```typescript
import { createClient } from '@/data-access/supabase/next-server';
import { SomeUtil } from '@/utils/some-util';
```

## 📝 Príklady použitia

### Kompletný príklad v React komponente:
```typescript
import { createClient } from '@/data-access/supabase/next-server';
import { cookies } from 'next/headers';
import { Button } from '@/ui/components/button';
import { formatDate, formatRelativeTime } from '@/utils/date';
import { Todo, User } from '@/domain/entities';
import { validateUser } from '@/utils/validation';

export default async function TodoPage() {
  const cookieStore = cookies();
  const supabase = await createClient(cookieStore);
  
  const { data: todos } = await supabase.from('todos').select();
  
  return (
    <div>
      <h1>Todos</h1>
      <Button variant="primary">Pridať Todo</Button>
      {todos?.map((todo: Todo) => (
        <div key={todo.id}>
          <h3>{todo.title}</h3>
          <p>Vytvorené: {formatDate(new Date(todo.createdAt))}</p>
        </div>
      ))}
    </div>
  );
}
```

## ⚙️ Konfigurácia

### TypeScript paths v `tsconfig.base.json`:
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["*"],
      "@/domain/*": ["libs/domain/src/*"],
      "@/data-access/*": ["libs/data-access/src/*"],
      "@/ui/*": ["libs/ui/src/*"],
      "@/utils/*": ["libs/util/src/*"],
      
      // Barrel exports
      "@/domain": ["libs/domain/src/index.ts"],
      "@/data-access": ["libs/data-access/src/index.ts"],
      "@/ui": ["libs/ui/src/index.ts"],
      "@/utils": ["libs/util/src/index.ts"]
    }
  }
}
```

## 🎨 Výhody nového systému

1. **Kratšie importy**: `@/ui/button` namiesto `@dohoder/ui/components/button`
2. **Jasná hierarchia**: Priame importy zo špecifických súborov
3. **Flexibilita**: Možnosť barrel importov aj špecifických importov
4. **Lepšia čitateľnosť**: Okamžite jasné odkial pochádza import
5. **IDE podpora**: Lepšie auto-complete a navigácia

## 🔧 Best Practices

1. **Používaj špecifické importy** pre lepšiu tree-shaking optimalizáciu:
   ```typescript
   // ✅ Dobré
   import { Button } from '@/ui/components/button';
   
   // ❌ Menej optimálne (ale stále OK)
   import { Button } from '@/ui';
   ```

2. **Organizuj importy** podľa typu:
   ```typescript
   // External libraries
   import React from 'react';
   import { cookies } from 'next/headers';
   
   // Internal libraries
   import { createClient } from '@/data-access/supabase/next-server';
   import { Button } from '@/ui/components/button';
   import { formatDate } from '@/utils/date';
   import { Todo } from '@/domain/entities/todo';
   ```

3. **Používaj barrel exports** pre pohodlné importy súvisiacich modulov:
   ```typescript
   // entities/index.ts
   export * from './user';
   export * from './todo';
   export * from './project';
   ```

## 🚀 Budúce rozšírenia

Systém je pripravený na ďalšie knižnice:
- `@/hooks` - Custom React hooks
- `@/constants` - Aplikačné konštanty
- `@/types` - Globálne TypeScript typy
- `@/config` - Konfiguračné súbory

-- Temporary migration to disable <PERSON><PERSON> for development testing
-- This should be reverted in production!

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Users can view their own todos" ON public.todos;
DROP POLICY IF EXISTS "Users can insert their own todos" ON public.todos;
DROP POLICY IF EXISTS "Users can update their own todos" ON public.todos;
DROP POLICY IF EXISTS "Users can delete their own todos" ON public.todos;

-- Create permissive policies for development
CREATE POLICY "Allow all operations on todos for development" ON public.todos
    FOR ALL USING (true) WITH CHECK (true);

-- Same for users table
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;

CREATE POLICY "Allow all operations on users for development" ON public.users
    FOR ALL USING (true) WITH CHECK (true);

-- Same for categories table
DROP POLICY IF EXISTS "Users can view their own categories" ON public.categories;
DROP POLICY IF EXISTS "Users can insert their own categories" ON public.categories;
DROP POLICY IF EXISTS "Users can update their own categories" ON public.categories;
DROP POLICY IF EXISTS "Users can delete their own categories" ON public.categories;

CREATE POLICY "Allow all operations on categories for development" ON public.categories
    FOR ALL USING (true) WITH CHECK (true);

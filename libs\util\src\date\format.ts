/**
 * Format date utilities
 */

export function formatDate(date: Date, format: 'short' | 'long' | 'iso' = 'short'): string {
  switch (format) {
    case 'short':
      return date.toLocaleDateString('sk-SK');
    case 'long':
      return date.toLocaleDateString('sk-SK', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    case 'iso':
      return date.toISOString();
    default:
      return date.toLocaleDateString('sk-SK');
  }
}

export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'práve teraz';
  } else if (diffInMinutes < 60) {
    return `pred ${diffInMinutes} minútami`;
  } else if (diffInHours < 24) {
    return `pred ${diffInHours} hodinami`;
  } else {
    return `pred ${diffInDays} dňami`;
  }
}

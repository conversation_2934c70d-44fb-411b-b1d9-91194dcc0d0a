-- Seed data for development
-- This file is used to populate the database with initial data for development

-- Insert sample users (using fixed UUIDs for consistency)
INSERT INTO public.users (id, email, name, created_at, updated_at) VALUES
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '<PERSON>', NOW() - INTERVAL '7 days', NOW() - INTERVAL '7 days'),
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '<PERSON>', NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),
    ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '<PERSON>', NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days')
ON CONFLICT (id) DO NOTHING;

-- Insert sample todos
INSERT INTO public.todos (title, description, completed, user_id, created_at, updated_at) VALUES
    -- <PERSON>'s todos
    ('Dokončiť projekt', '<PERSON><PERSON><PERSON><PERSON><PERSON> React Native aplikáciu s Supabase', false, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '6 days', NOW() - INTERVAL '6 days'),
    ('Nakúpiť potraviny', 'Mlieko, chlieb, vajcia, ovocie', true, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '5 days', NOW() - INTERVAL '2 days'),
    ('Zavolať lekárovi', 'Dohodnúť termín na kontrolu', false, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '4 days', NOW() - INTERVAL '4 days'),
    
    -- Jane Smith's todos
    ('Prečítať knihu', 'Dokončiť čítanie "Clean Code"', false, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '4 days', NOW() - INTERVAL '4 days'),
    ('Cvičenie', 'Ísť do fitka 3x týždenne', true, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '3 days', NOW() - INTERVAL '1 day'),
    ('Napísať blog post', 'Článok o React Query a Supabase', false, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days'),
    
    -- Bob Wilson's todos
    ('Opraviť auto', 'Vymeniť olej a filter', false, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '2 days', NOW() - INTERVAL '2 days'),
    ('Upratať dom', 'Generálne upratovanie', true, '550e8400-e29b-41d4-a716-************', NOW() - INTERVAL '1 day', NOW() - INTERVAL '1 day')
ON CONFLICT DO NOTHING;

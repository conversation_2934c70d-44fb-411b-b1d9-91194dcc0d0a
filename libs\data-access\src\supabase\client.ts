import { createClient, SupabaseClient } from '@supabase/supabase-js';

// R<PERSON><PERSON><PERSON>šime podľa prostredia
const isExpo =
  typeof navigator !== 'undefined' && navigator.product === 'ReactNative';

const supabaseUrl = isExpo
  ? process.env.EXPO_PUBLIC_SUPABASE_URL
  : process.env.NEXT_PUBLIC_SUPABASE_URL;

const supabaseKey = isExpo
  ? process.env.EXPO_PUBLIC_SUPABASE_KEY
  : process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables.');
}

export const supabaseClient = (): SupabaseClient => {
  return createClient(supabaseUrl, supabaseKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: !isExpo, // Expo nepotrebuje URL callback
      flowType: 'pkce',
      storageKey: 'supabase.auth.token',
    },
  });
};
